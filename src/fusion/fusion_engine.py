"""
融合引擎主类
Fusion Engine Main Class
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any, Union
from enum import Enum
from pathlib import Path

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..video.video_loader import VideoLoader
from .insertion_fusion import InsertionFusion, InsertionPosition, InsertionMode


class FusionType(Enum):
    """融合类型枚举"""
    INSERTION = "insertion"  # 插入融合
    OVERLAY = "overlay"      # 叠加融合
    BLEND = "blend"          # 混合融合


class FusionParams:
    """融合参数类"""
    
    def __init__(self):
        self.fusion_type: FusionType = FusionType.INSERTION
        self.insertion_mode: InsertionMode = InsertionMode.DIRECT
        self.resize_mode: str = "fit"
        self.alpha: float = 0.5
        self.positions: List[InsertionPosition] = []
        self.output_fps: float = 30.0
        self.output_codec: str = 'mp4v'
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'fusion_type': self.fusion_type.value,
            'insertion_mode': self.insertion_mode.value,
            'resize_mode': self.resize_mode,
            'alpha': self.alpha,
            'positions': [{'frame': pos.frame_number, 'duration': pos.duration} 
                         for pos in self.positions],
            'output_fps': self.output_fps,
            'output_codec': self.output_codec
        }
    
    def from_dict(self, data: Dict[str, Any]):
        """从字典加载"""
        self.fusion_type = FusionType(data.get('fusion_type', 'insertion'))
        self.insertion_mode = InsertionMode(data.get('insertion_mode', 'direct'))
        self.resize_mode = data.get('resize_mode', 'fit')
        self.alpha = data.get('alpha', 0.5)
        self.output_fps = data.get('output_fps', 30.0)
        self.output_codec = data.get('output_codec', 'mp4v')
        
        # 加载位置信息
        positions_data = data.get('positions', [])
        self.positions = [InsertionPosition(pos['frame'], pos['duration']) 
                         for pos in positions_data]


class FusionEngine:
    """融合引擎主类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.config = ConfigManager()
        
        # 视频加载器
        self.video_a_loader: Optional[VideoLoader] = None
        self.video_b_loader: Optional[VideoLoader] = None
        
        # 融合器
        self.insertion_fusion = InsertionFusion()
        
        # 融合参数
        self.fusion_params = FusionParams()
        
        # 结果
        self.result_frames: List[np.ndarray] = []
        
        self.logger.info("融合引擎初始化完成")
    
    def load_video_a(self, file_path: str) -> bool:
        """加载A视频"""
        try:
            self.video_a_loader = VideoLoader()
            video_info = self.video_a_loader.load_video(file_path)
            
            if video_info is None:
                self.logger.error(f"加载A视频失败: {file_path}")
                return False
            
            self.logger.info(f"A视频加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载A视频时发生错误: {e}")
            return False
    
    def load_video_b(self, file_path: str) -> bool:
        """加载B视频"""
        try:
            self.video_b_loader = VideoLoader()
            video_info = self.video_b_loader.load_video(file_path)
            
            if video_info is None:
                self.logger.error(f"加载B视频失败: {file_path}")
                return False
            
            self.logger.info(f"B视频加载成功: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载B视频时发生错误: {e}")
            return False
    
    def set_fusion_params(self, params: FusionParams):
        """设置融合参数"""
        # 保留已有的插入位置
        existing_positions = self.fusion_params.positions.copy()
        self.fusion_params = params
        if not self.fusion_params.positions and existing_positions:
            self.fusion_params.positions = existing_positions
        self.logger.info(f"融合参数已设置: {params.to_dict()}")
    
    def add_insertion_position(self, frame_number: int, duration: int = 1):
        """添加插入位置"""
        position = InsertionPosition(frame_number, duration)
        self.fusion_params.positions.append(position)
        self.logger.info(f"添加插入位置: {position}")
    
    def clear_insertion_positions(self):
        """清除所有插入位置"""
        self.fusion_params.positions.clear()
        self.logger.info("已清除所有插入位置")
    
    def get_video_info(self) -> Dict[str, Any]:
        """获取视频信息"""
        info = {
            'video_a': None,
            'video_b': None,
            'compatible': False
        }
        
        if self.video_a_loader and self.video_a_loader.is_loaded():
            info['video_a'] = self.video_a_loader.get_current_info().to_dict()
        
        if self.video_b_loader and self.video_b_loader.is_loaded():
            info['video_b'] = self.video_b_loader.get_current_info().to_dict()
        
        # 检查兼容性
        if info['video_a'] and info['video_b']:
            info['compatible'] = True  # 基本兼容性检查
        
        return info
    
    def validate_fusion_params(self) -> Tuple[bool, str]:
        """验证融合参数"""
        try:
            # 检查视频是否加载
            if not self.video_a_loader or not self.video_a_loader.is_loaded():
                return False, "A视频未加载"
            
            if not self.video_b_loader or not self.video_b_loader.is_loaded():
                return False, "B视频未加载"
            
            # 检查插入位置
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                if not self.fusion_params.positions:
                    return False, "未设置插入位置"
                
                video_b_info = self.video_b_loader.get_current_info()
                for pos in self.fusion_params.positions:
                    if pos.frame_number < 0 or pos.frame_number >= video_b_info.frame_count:
                        return False, f"插入位置 {pos.frame_number} 超出B视频范围"
                    
                    if pos.duration <= 0:
                        return False, f"插入持续时间必须大于0"
            
            # 检查其他参数
            if self.fusion_params.alpha < 0 or self.fusion_params.alpha > 1:
                return False, "透明度值必须在0-1之间"
            
            if self.fusion_params.output_fps <= 0:
                return False, "输出帧率必须大于0"
            
            return True, "参数验证通过"
            
        except Exception as e:
            return False, f"参数验证失败: {e}"
    
    def execute_fusion(self) -> bool:
        """执行融合"""
        try:
            # 验证参数
            is_valid, message = self.validate_fusion_params()
            if not is_valid:
                self.logger.error(f"融合参数验证失败: {message}")
                return False
            
            self.logger.info("开始执行融合")
            
            # 根据融合类型执行相应的融合
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                success = self._execute_insertion_fusion()
            else:
                self.logger.error(f"不支持的融合类型: {self.fusion_params.fusion_type}")
                return False
            
            if success:
                self.logger.info("融合执行完成")
                return True
            else:
                self.logger.error("融合执行失败")
                return False
                
        except Exception as e:
            self.logger.error(f"执行融合时发生错误: {e}")
            return False
    
    def _execute_insertion_fusion(self) -> bool:
        """执行插入融合"""
        try:
            # 设置视频
            self.insertion_fusion.set_videos(self.video_a_loader, self.video_b_loader)
            
            # 根据插入模式执行融合
            if self.fusion_params.insertion_mode == InsertionMode.DIRECT:
                self.result_frames = self.insertion_fusion.direct_insertion(
                    self.fusion_params.positions, 
                    self.fusion_params.resize_mode
                )
            elif self.fusion_params.insertion_mode == InsertionMode.REPLACE:
                self.result_frames = self.insertion_fusion.replace_insertion(
                    self.fusion_params.positions, 
                    self.fusion_params.resize_mode
                )
            else:
                self.logger.error(f"不支持的插入模式: {self.fusion_params.insertion_mode}")
                return False
            
            return len(self.result_frames) > 0
            
        except Exception as e:
            self.logger.error(f"执行插入融合失败: {e}")
            return False
    
    def get_fusion_preview(self, max_frames: int = 5) -> List[Tuple[int, np.ndarray]]:
        """获取融合预览"""
        try:
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                return self.insertion_fusion.get_insertion_preview(
                    self.fusion_params.positions, max_frames
                )
            else:
                self.logger.warning(f"暂不支持 {self.fusion_params.fusion_type} 类型的预览")
                return []
                
        except Exception as e:
            self.logger.error(f"获取融合预览失败: {e}")
            return []
    
    def save_result(self, output_path: str) -> bool:
        """保存融合结果"""
        try:
            if not self.result_frames:
                self.logger.error("没有可保存的融合结果")
                return False
            
            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存视频
            if self.fusion_params.fusion_type == FusionType.INSERTION:
                return self.insertion_fusion.save_result_video(
                    output_path,
                    self.fusion_params.output_fps,
                    self.fusion_params.output_codec
                )
            else:
                self.logger.error(f"不支持的融合类型保存: {self.fusion_params.fusion_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"保存融合结果失败: {e}")
            return False
    
    def get_result_frames(self) -> List[np.ndarray]:
        """获取结果帧"""
        return self.result_frames.copy()
    
    def get_result_info(self) -> Dict[str, Any]:
        """获取结果信息"""
        if not self.result_frames:
            return {}
        
        first_frame = self.result_frames[0]
        h, w = first_frame.shape[:2]
        
        return {
            'frame_count': len(self.result_frames),
            'width': w,
            'height': h,
            'duration': len(self.result_frames) / self.fusion_params.output_fps,
            'fps': self.fusion_params.output_fps
        }
    
    def save_fusion_params(self, file_path: str) -> bool:
        """保存融合参数到文件"""
        try:
            import json
            
            params_dict = self.fusion_params.to_dict()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(params_dict, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"融合参数已保存到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存融合参数失败: {e}")
            return False
    
    def load_fusion_params(self, file_path: str) -> bool:
        """从文件加载融合参数"""
        try:
            import json
            
            with open(file_path, 'r', encoding='utf-8') as f:
                params_dict = json.load(f)
            
            self.fusion_params.from_dict(params_dict)
            
            self.logger.info(f"融合参数已从文件加载: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载融合参数失败: {e}")
            return False
    
    def reset(self):
        """重置融合引擎"""
        self.video_a_loader = None
        self.video_b_loader = None
        self.result_frames.clear()
        self.fusion_params = FusionParams()
        
        self.logger.info("融合引擎已重置")
