"""
配置管理器
Configuration Manager
"""

import json
import os
from typing import Any, Dict


class ConfigManager:
    """配置管理器类"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self.load_config()
    
    def load_config(self, config_file: str = "config.json"):
        """加载配置文件"""
        try:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(project_root, config_file)
            
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
            else:
                # 默认配置
                self._config = self._get_default_config()
                
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config = self._get_default_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, config_file: str = "config.json"):
        """保存配置文件"""
        try:
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_path = os.path.join(project_root, config_file)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "application": {
                "name": "视频融合编辑器",
                "version": "1.0.0",
                "window": {
                    "width": 1200,
                    "height": 800,
                    "min_width": 800,
                    "min_height": 600
                }
            },
            "video": {
                "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".wmv"],
                "max_file_size_mb": 1000,
                "preview_fps": 30
            },
            "fusion": {
                "default_blend_alpha": 0.5,
                "edge_detection_threshold": 100,
                "histogram_bins": 256
            },
            "effects": {
                "text_overlay": {
                    "default_font": "Arial",
                    "default_size": 24,
                    "default_color": [255, 255, 255]
                }
            },
            "logging": {
                "level": "INFO",
                "file": "logs/video_fusion_editor.log",
                "max_size_mb": 10,
                "backup_count": 5
            }
        }
