"""
控制面板组件
Control Panel Component
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                             QLabel, QSlider, QSpinBox, QComboBox, QPushButton,
                             QCheckBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager


class ControlPanel(QWidget):
    """控制面板组件"""
    
    # 信号定义
    fusion_params_changed = pyqtSignal(dict)  # 融合参数改变
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.logger = Logger.get_logger(__name__)
        self.config = ConfigManager()
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 融合模式组
        self.create_fusion_mode_group(layout)
        
        # 融合参数组
        self.create_fusion_params_group(layout)
        
        # 预处理参数组
        self.create_preprocessing_group(layout)
        
        # 文字叠加组
        self.create_text_overlay_group(layout)
        
        layout.addStretch()
    
    def create_fusion_mode_group(self, parent_layout):
        """创建融合模式组"""
        group = QGroupBox("融合模式")
        layout = QVBoxLayout(group)
        
        self.fusion_mode_combo = QComboBox()
        self.fusion_mode_combo.addItems([
            "直接插入",
            "预处理插入", 
            "叠加覆盖",
            "线性加权融合",
            "分段插入"
        ])
        self.fusion_mode_combo.currentTextChanged.connect(self.on_fusion_mode_changed)
        layout.addWidget(self.fusion_mode_combo)
        
        parent_layout.addWidget(group)
    
    def create_fusion_params_group(self, parent_layout):
        """创建融合参数组"""
        group = QGroupBox("融合参数")
        layout = QVBoxLayout(group)
        
        # 透明度控制
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("透明度:"))
        self.alpha_slider = QSlider(Qt.Horizontal)
        self.alpha_slider.setMinimum(0)
        self.alpha_slider.setMaximum(100)
        self.alpha_slider.setValue(int(self.config.get("fusion.default_blend_alpha", 0.5) * 100))
        self.alpha_slider.valueChanged.connect(self.on_params_changed)
        alpha_layout.addWidget(self.alpha_slider)
        
        self.alpha_label = QLabel("50%")
        self.alpha_slider.valueChanged.connect(lambda v: self.alpha_label.setText(f"{v}%"))
        alpha_layout.addWidget(self.alpha_label)
        layout.addLayout(alpha_layout)
        
        # 插入位置
        position_layout = QHBoxLayout()
        position_layout.addWidget(QLabel("插入位置:"))
        self.position_spinbox = QSpinBox()
        self.position_spinbox.setMinimum(0)
        self.position_spinbox.setMaximum(9999)
        self.position_spinbox.setSuffix(" 帧")
        self.position_spinbox.valueChanged.connect(self.on_params_changed)
        position_layout.addWidget(self.position_spinbox)
        layout.addLayout(position_layout)
        
        # 融合区域
        region_layout = QHBoxLayout()
        region_layout.addWidget(QLabel("融合区域:"))
        self.region_combo = QComboBox()
        self.region_combo.addItems(["全画面", "固定位置", "连续移动"])
        self.region_combo.currentTextChanged.connect(self.on_params_changed)
        region_layout.addWidget(self.region_combo)
        layout.addLayout(region_layout)
        
        parent_layout.addWidget(group)
    
    def create_preprocessing_group(self, parent_layout):
        """创建预处理参数组"""
        group = QGroupBox("预处理")
        layout = QVBoxLayout(group)
        
        # 边缘检测
        self.edge_detection_cb = QCheckBox("边缘检测")
        self.edge_detection_cb.stateChanged.connect(self.on_params_changed)
        layout.addWidget(self.edge_detection_cb)
        
        # 边缘检测阈值
        edge_threshold_layout = QHBoxLayout()
        edge_threshold_layout.addWidget(QLabel("边缘阈值:"))
        self.edge_threshold_spinbox = QSpinBox()
        self.edge_threshold_spinbox.setMinimum(1)
        self.edge_threshold_spinbox.setMaximum(255)
        self.edge_threshold_spinbox.setValue(self.config.get("fusion.edge_detection_threshold", 100))
        self.edge_threshold_spinbox.valueChanged.connect(self.on_params_changed)
        edge_threshold_layout.addWidget(self.edge_threshold_spinbox)
        layout.addLayout(edge_threshold_layout)
        
        # 直方图匹配
        self.histogram_match_cb = QCheckBox("直方图匹配")
        self.histogram_match_cb.stateChanged.connect(self.on_params_changed)
        layout.addWidget(self.histogram_match_cb)
        
        # 缩放
        scale_layout = QHBoxLayout()
        scale_layout.addWidget(QLabel("缩放比例:"))
        self.scale_spinbox = QDoubleSpinBox()
        self.scale_spinbox.setMinimum(0.1)
        self.scale_spinbox.setMaximum(5.0)
        self.scale_spinbox.setValue(1.0)
        self.scale_spinbox.setSingleStep(0.1)
        self.scale_spinbox.valueChanged.connect(self.on_params_changed)
        scale_layout.addWidget(self.scale_spinbox)
        layout.addLayout(scale_layout)
        
        parent_layout.addWidget(group)
    
    def create_text_overlay_group(self, parent_layout):
        """创建文字叠加组"""
        group = QGroupBox("文字叠加")
        layout = QVBoxLayout(group)
        
        # 启用文字叠加
        self.text_overlay_cb = QCheckBox("启用文字叠加")
        self.text_overlay_cb.stateChanged.connect(self.on_params_changed)
        layout.addWidget(self.text_overlay_cb)
        
        # 字体大小
        font_size_layout = QHBoxLayout()
        font_size_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setMinimum(8)
        self.font_size_spinbox.setMaximum(72)
        self.font_size_spinbox.setValue(self.config.get("effects.text_overlay.default_size", 24))
        self.font_size_spinbox.valueChanged.connect(self.on_params_changed)
        font_size_layout.addWidget(self.font_size_spinbox)
        layout.addLayout(font_size_layout)
        
        # 文字位置
        text_pos_layout = QHBoxLayout()
        text_pos_layout.addWidget(QLabel("文字位置:"))
        self.text_position_combo = QComboBox()
        self.text_position_combo.addItems(["左上", "右上", "左下", "右下", "中心", "自定义"])
        self.text_position_combo.currentTextChanged.connect(self.on_params_changed)
        text_pos_layout.addWidget(self.text_position_combo)
        layout.addLayout(text_pos_layout)
        
        parent_layout.addWidget(group)
    
    def on_fusion_mode_changed(self, mode: str):
        """融合模式改变"""
        self.logger.info(f"融合模式改变: {mode}")
        self.on_params_changed()
    
    def on_params_changed(self):
        """参数改变"""
        params = self.get_current_params()
        self.fusion_params_changed.emit(params)
        self.logger.debug(f"融合参数改变: {params}")
    
    def get_current_params(self) -> dict:
        """获取当前参数"""
        return {
            "fusion_mode": self.fusion_mode_combo.currentText(),
            "alpha": self.alpha_slider.value() / 100.0,
            "insert_position": self.position_spinbox.value(),
            "fusion_region": self.region_combo.currentText(),
            "edge_detection": self.edge_detection_cb.isChecked(),
            "edge_threshold": self.edge_threshold_spinbox.value(),
            "histogram_match": self.histogram_match_cb.isChecked(),
            "scale": self.scale_spinbox.value(),
            "text_overlay": self.text_overlay_cb.isChecked(),
            "font_size": self.font_size_spinbox.value(),
            "text_position": self.text_position_combo.currentText()
        }
