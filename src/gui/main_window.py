"""
主窗口类
Main Window Class
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QMenuBar, QToolBar, QStatusBar, QSplitter, 
                             QGroupBox, QLabel, QPushButton, QFileDialog,
                             QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap

from ..utils.config_manager import ConfigManager
from ..utils.logger import Logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    video_a_loaded = pyqtSignal(str)  # A视频加载信号
    video_b_loaded = pyqtSignal(str)  # B视频加载信号
    
    def __init__(self):
        super().__init__()
        
        # 初始化配置和日志
        self.config = ConfigManager()
        self.logger = Logger.get_logger(__name__)
        
        # 初始化UI
        self.init_ui()
        
        # 连接信号
        self.connect_signals()
        
        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle(self.config.get("application.name", "视频融合编辑器"))
        self.setGeometry(100, 100, 
                        self.config.get("application.window.width", 1200),
                        self.config.get("application.window.height", 800))
        self.setMinimumSize(self.config.get("application.window.min_width", 800),
                           self.config.get("application.window.min_height", 600))
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 加载A视频
        load_a_action = file_menu.addAction('加载A视频(&A)')
        load_a_action.setShortcut('Ctrl+A')
        load_a_action.triggered.connect(self.load_video_a)
        
        # 加载B视频
        load_b_action = file_menu.addAction('加载B视频(&B)')
        load_b_action.setShortcut('Ctrl+B')
        load_b_action.triggered.connect(self.load_video_b)
        
        file_menu.addSeparator()
        
        # 导出视频
        export_action = file_menu.addAction('导出视频(&E)')
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_video)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = file_menu.addAction('退出(&X)')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        # 撤销
        undo_action = edit_menu.addAction('撤销(&U)')
        undo_action.setShortcut('Ctrl+Z')
        
        # 重做
        redo_action = edit_menu.addAction('重做(&R)')
        redo_action.setShortcut('Ctrl+Y')
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 重置布局
        reset_layout_action = view_menu.addAction('重置布局(&R)')
        reset_layout_action.triggered.connect(self.reset_layout)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = help_menu.addAction('关于(&A)')
        about_action.triggered.connect(self.show_about)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar('主工具栏')
        toolbar.setMovable(False)
        
        # 加载视频按钮
        load_a_btn = toolbar.addAction('加载A视频')
        load_a_btn.triggered.connect(self.load_video_a)
        
        load_b_btn = toolbar.addAction('加载B视频')
        load_b_btn.triggered.connect(self.load_video_b)
        
        toolbar.addSeparator()
        
        # 融合控制按钮
        start_fusion_btn = toolbar.addAction('开始融合')
        start_fusion_btn.triggered.connect(self.start_fusion)
        
        stop_fusion_btn = toolbar.addAction('停止融合')
        stop_fusion_btn.triggered.connect(self.stop_fusion)
        
        toolbar.addSeparator()
        
        # 导出按钮
        export_btn = toolbar.addAction('导出视频')
        export_btn.triggered.connect(self.export_video)
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧视频区域
        self.create_video_area(splitter)
        
        # 右侧控制面板
        self.create_control_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
    
    def create_video_area(self, parent):
        """创建视频显示区域"""
        video_widget = QWidget()
        video_layout = QVBoxLayout(video_widget)
        
        # A视频组
        a_group = QGroupBox("A视频 (源视频)")
        a_layout = QVBoxLayout(a_group)
        
        self.video_a_label = QLabel("点击加载A视频")
        self.video_a_label.setAlignment(Qt.AlignCenter)
        self.video_a_label.setMinimumHeight(200)
        self.video_a_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")
        a_layout.addWidget(self.video_a_label)
        
        # A视频控制按钮
        a_btn_layout = QHBoxLayout()
        self.load_a_btn = QPushButton("加载A视频")
        self.load_a_btn.clicked.connect(self.load_video_a)
        a_btn_layout.addWidget(self.load_a_btn)
        a_btn_layout.addStretch()
        a_layout.addLayout(a_btn_layout)
        
        video_layout.addWidget(a_group)
        
        # B视频组
        b_group = QGroupBox("B视频 (目标视频)")
        b_layout = QVBoxLayout(b_group)
        
        self.video_b_label = QLabel("点击加载B视频")
        self.video_b_label.setAlignment(Qt.AlignCenter)
        self.video_b_label.setMinimumHeight(200)
        self.video_b_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")
        b_layout.addWidget(self.video_b_label)
        
        # B视频控制按钮
        b_btn_layout = QHBoxLayout()
        self.load_b_btn = QPushButton("加载B视频")
        self.load_b_btn.clicked.connect(self.load_video_b)
        b_btn_layout.addWidget(self.load_b_btn)
        b_btn_layout.addStretch()
        b_layout.addLayout(b_btn_layout)
        
        video_layout.addWidget(b_group)
        
        parent.addWidget(video_widget)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        
        # 融合参数组
        fusion_group = QGroupBox("融合参数")
        fusion_layout = QVBoxLayout(fusion_group)
        
        # 这里将添加各种融合参数控件
        fusion_placeholder = QLabel("融合参数控制面板\n(待开发)")
        fusion_placeholder.setAlignment(Qt.AlignCenter)
        fusion_placeholder.setStyleSheet("color: #666; font-style: italic;")
        fusion_layout.addWidget(fusion_placeholder)
        
        control_layout.addWidget(fusion_group)
        
        # 预览组
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("融合预览")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(150)
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f5f5f5;")
        preview_layout.addWidget(self.preview_label)
        
        control_layout.addWidget(preview_group)
        
        # 操作按钮组
        action_group = QGroupBox("操作")
        action_layout = QVBoxLayout(action_group)
        
        self.start_btn = QPushButton("开始融合")
        self.start_btn.clicked.connect(self.start_fusion)
        action_layout.addWidget(self.start_btn)
        
        self.export_btn = QPushButton("导出视频")
        self.export_btn.clicked.connect(self.export_video)
        self.export_btn.setEnabled(False)
        action_layout.addWidget(self.export_btn)
        
        control_layout.addWidget(action_group)
        
        control_layout.addStretch()
        
        parent.addWidget(control_widget)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def connect_signals(self):
        """连接信号"""
        self.video_a_loaded.connect(self.on_video_a_loaded)
        self.video_b_loaded.connect(self.on_video_b_loaded)
    
    def load_video_a(self):
        """加载A视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择A视频文件", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )
        
        if file_path:
            self.video_a_loaded.emit(file_path)
    
    def load_video_b(self):
        """加载B视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择B视频文件", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )
        
        if file_path:
            self.video_b_loaded.emit(file_path)
    
    def on_video_a_loaded(self, file_path: str):
        """A视频加载完成"""
        self.video_a_label.setText(f"A视频已加载:\n{file_path}")
        self.status_label.setText(f"A视频已加载: {file_path}")
        self.logger.info(f"A视频已加载: {file_path}")
    
    def on_video_b_loaded(self, file_path: str):
        """B视频加载完成"""
        self.video_b_label.setText(f"B视频已加载:\n{file_path}")
        self.status_label.setText(f"B视频已加载: {file_path}")
        self.logger.info(f"B视频已加载: {file_path}")
    
    def start_fusion(self):
        """开始融合"""
        self.status_label.setText("开始融合处理...")
        self.logger.info("开始融合处理")
        # TODO: 实现融合逻辑
    
    def stop_fusion(self):
        """停止融合"""
        self.status_label.setText("融合已停止")
        self.logger.info("融合已停止")
        # TODO: 实现停止逻辑
    
    def export_video(self):
        """导出视频"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存融合视频", "", 
            "MP4文件 (*.mp4);;AVI文件 (*.avi);;所有文件 (*)"
        )
        
        if file_path:
            self.status_label.setText(f"导出视频到: {file_path}")
            self.logger.info(f"导出视频到: {file_path}")
            # TODO: 实现导出逻辑
    
    def reset_layout(self):
        """重置布局"""
        self.status_label.setText("布局已重置")
        self.logger.info("布局已重置")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         f"{self.config.get('application.name')}\n"
                         f"版本: {self.config.get('application.version')}\n\n"
                         "桌面端视频编辑应用，实现A视频与B视频的多维度融合功能。")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.logger.info("应用程序退出")
        event.accept()
