"""
主窗口类
Main Window Class
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QMenuBar, QToolBar, QStatusBar, QSplitter,
                             QGroupBox, QLabel, QPushButton, QFileDialog,
                             QMessageBox, QProgressBar, QTextEdit, QTabWidget,
                             QAction, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QPixmap, QDragEnterEvent, QDropEvent

from ..utils.config_manager import ConfigManager
from ..utils.logger import Logger
from ..fusion.fusion_engine import FusionEngine, FusionParams, FusionType
from ..video.video_loader import VideoLoader
from .control_panel import ControlPanel
from .preset_manager import PresetManager


class FusionThread(QThread):
    """融合处理线程"""

    finished = pyqtSignal(bool)  # 完成信号
    error = pyqtSignal(str)      # 错误信号
    progress = pyqtSignal(int)   # 进度信号

    def __init__(self, fusion_engine: FusionEngine):
        super().__init__()
        self.fusion_engine = fusion_engine

    def run(self):
        """运行融合处理"""
        try:
            success = self.fusion_engine.execute_fusion()
            self.finished.emit(success)
        except Exception as e:
            self.error.emit(str(e))


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    video_a_loaded = pyqtSignal(str)  # A视频加载信号
    video_b_loaded = pyqtSignal(str)  # B视频加载信号
    
    def __init__(self):
        super().__init__()

        # 初始化配置和日志
        self.config = ConfigManager()
        self.logger = Logger.get_logger(__name__)

        # 初始化核心组件
        self.fusion_engine = FusionEngine()
        self.video_a_loader = VideoLoader()
        self.video_b_loader = VideoLoader()

        # 状态变量
        self.is_fusion_running = False
        self.current_video_a_path = None
        self.current_video_b_path = None

        # 初始化UI
        self.init_ui()

        # 连接信号
        self.connect_signals()

        # 启用拖拽
        self.setAcceptDrops(True)

        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle(self.config.get("application.name", "视频融合编辑器"))
        self.setGeometry(100, 100, 
                        self.config.get("application.window.width", 1200),
                        self.config.get("application.window.height", 800))
        self.setMinimumSize(self.config.get("application.window.min_width", 800),
                           self.config.get("application.window.min_height", 600))
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 新建项目
        new_project_action = file_menu.addAction('新建项目(&N)')
        new_project_action.setShortcut('Ctrl+N')
        new_project_action.triggered.connect(self.new_project)

        # 打开项目
        open_project_action = file_menu.addAction('打开项目(&O)')
        open_project_action.setShortcut('Ctrl+O')
        open_project_action.triggered.connect(self.open_project)

        # 保存项目
        save_project_action = file_menu.addAction('保存项目(&S)')
        save_project_action.setShortcut('Ctrl+S')
        save_project_action.triggered.connect(self.save_project)

        file_menu.addSeparator()

        # 加载A视频
        load_a_action = file_menu.addAction('加载A视频(&A)')
        load_a_action.setShortcut('Ctrl+Shift+A')
        load_a_action.triggered.connect(self.load_video_a)

        # 加载B视频
        load_b_action = file_menu.addAction('加载B视频(&B)')
        load_b_action.setShortcut('Ctrl+Shift+B')
        load_b_action.triggered.connect(self.load_video_b)

        file_menu.addSeparator()

        # 导出视频
        export_action = file_menu.addAction('导出视频(&E)')
        export_action.setShortcut('Ctrl+E')
        export_action.triggered.connect(self.export_video)

        # 批量处理
        batch_action = file_menu.addAction('批量处理(&T)')
        batch_action.setShortcut('Ctrl+T')
        batch_action.triggered.connect(self.batch_process)

        file_menu.addSeparator()

        # 退出
        exit_action = file_menu.addAction('退出(&X)')
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')

        # 撤销
        undo_action = edit_menu.addAction('撤销(&U)')
        undo_action.setShortcut('Ctrl+Z')
        undo_action.setEnabled(False)  # 暂时禁用

        # 重做
        redo_action = edit_menu.addAction('重做(&R)')
        redo_action.setShortcut('Ctrl+Y')
        redo_action.setEnabled(False)  # 暂时禁用

        edit_menu.addSeparator()

        # 参数预设
        presets_action = edit_menu.addAction('参数预设(&P)')
        presets_action.setShortcut('Ctrl+P')
        presets_action.triggered.connect(self.open_preset_manager)

        # 融合菜单
        fusion_menu = menubar.addMenu('融合(&F)')

        # 开始融合
        start_fusion_action = fusion_menu.addAction('开始融合(&S)')
        start_fusion_action.setShortcut('F5')
        start_fusion_action.triggered.connect(self.start_fusion)

        # 停止融合
        stop_fusion_action = fusion_menu.addAction('停止融合(&T)')
        stop_fusion_action.setShortcut('F6')
        stop_fusion_action.triggered.connect(self.stop_fusion)

        fusion_menu.addSeparator()

        # 生成预览
        preview_action = fusion_menu.addAction('生成预览(&P)')
        preview_action.setShortcut('F7')
        preview_action.triggered.connect(self.generate_preview)

        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')

        # 重置布局
        reset_layout_action = view_menu.addAction('重置布局(&R)')
        reset_layout_action.triggered.connect(self.reset_layout)

        view_menu.addSeparator()

        # 显示性能监控
        performance_action = view_menu.addAction('性能监控(&M)')
        performance_action.setCheckable(True)
        performance_action.triggered.connect(self.toggle_performance_monitor)

        # 工具菜单
        tools_menu = menubar.addMenu('工具(&T)')

        # 性能报告
        performance_report_action = tools_menu.addAction('导出性能报告(&R)')
        performance_report_action.triggered.connect(self.export_performance_report)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 用户手册
        manual_action = help_menu.addAction('用户手册(&M)')
        manual_action.setShortcut('F1')
        manual_action.triggered.connect(self.show_manual)

        help_menu.addSeparator()

        # 关于
        about_action = help_menu.addAction('关于(&A)')
        about_action.triggered.connect(self.show_about)
    
    def create_tool_bar(self):
        """创建工具栏"""
        # 主工具栏
        main_toolbar = self.addToolBar('主工具栏')
        main_toolbar.setMovable(False)

        # 项目操作
        new_project_btn = main_toolbar.addAction('新建')
        new_project_btn.triggered.connect(self.new_project)
        new_project_btn.setToolTip('新建项目 (Ctrl+N)')

        open_project_btn = main_toolbar.addAction('打开')
        open_project_btn.triggered.connect(self.open_project)
        open_project_btn.setToolTip('打开项目 (Ctrl+O)')

        save_project_btn = main_toolbar.addAction('保存')
        save_project_btn.triggered.connect(self.save_project)
        save_project_btn.setToolTip('保存项目 (Ctrl+S)')

        main_toolbar.addSeparator()

        # 加载视频按钮
        load_a_btn = main_toolbar.addAction('加载A视频')
        load_a_btn.triggered.connect(self.load_video_a)
        load_a_btn.setToolTip('加载A视频 (Ctrl+Shift+A)')

        load_b_btn = main_toolbar.addAction('加载B视频')
        load_b_btn.triggered.connect(self.load_video_b)
        load_b_btn.setToolTip('加载B视频 (Ctrl+Shift+B)')

        main_toolbar.addSeparator()

        # 融合控制按钮
        self.start_fusion_btn = main_toolbar.addAction('开始融合')
        self.start_fusion_btn.triggered.connect(self.start_fusion)
        self.start_fusion_btn.setToolTip('开始融合 (F5)')

        self.stop_fusion_btn = main_toolbar.addAction('停止融合')
        self.stop_fusion_btn.triggered.connect(self.stop_fusion)
        self.stop_fusion_btn.setToolTip('停止融合 (F6)')
        self.stop_fusion_btn.setEnabled(False)

        preview_btn = main_toolbar.addAction('预览')
        preview_btn.triggered.connect(self.generate_preview)
        preview_btn.setToolTip('生成预览 (F7)')

        main_toolbar.addSeparator()

        # 导出按钮
        export_btn = main_toolbar.addAction('导出')
        export_btn.triggered.connect(self.export_video)
        export_btn.setToolTip('导出视频 (Ctrl+E)')

        # 预设管理按钮
        presets_btn = main_toolbar.addAction('预设')
        presets_btn.triggered.connect(self.open_preset_manager)
        presets_btn.setToolTip('参数预设管理 (Ctrl+P)')
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧视频区域
        self.create_video_area(splitter)
        
        # 右侧控制面板
        self.create_control_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
    
    def create_video_area(self, parent):
        """创建视频显示区域"""
        video_widget = QWidget()
        video_layout = QVBoxLayout(video_widget)
        
        # A视频组
        a_group = QGroupBox("A视频 (源视频)")
        a_layout = QVBoxLayout(a_group)
        
        self.video_a_label = QLabel("点击加载A视频")
        self.video_a_label.setAlignment(Qt.AlignCenter)
        self.video_a_label.setMinimumHeight(200)
        self.video_a_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")
        a_layout.addWidget(self.video_a_label)
        
        # A视频控制按钮
        a_btn_layout = QHBoxLayout()
        self.load_a_btn = QPushButton("加载A视频")
        self.load_a_btn.clicked.connect(self.load_video_a)
        a_btn_layout.addWidget(self.load_a_btn)
        a_btn_layout.addStretch()
        a_layout.addLayout(a_btn_layout)
        
        video_layout.addWidget(a_group)
        
        # B视频组
        b_group = QGroupBox("B视频 (目标视频)")
        b_layout = QVBoxLayout(b_group)
        
        self.video_b_label = QLabel("点击加载B视频")
        self.video_b_label.setAlignment(Qt.AlignCenter)
        self.video_b_label.setMinimumHeight(200)
        self.video_b_label.setStyleSheet("border: 2px dashed #ccc; background-color: #f9f9f9;")
        b_layout.addWidget(self.video_b_label)
        
        # B视频控制按钮
        b_btn_layout = QHBoxLayout()
        self.load_b_btn = QPushButton("加载B视频")
        self.load_b_btn.clicked.connect(self.load_video_b)
        b_btn_layout.addWidget(self.load_b_btn)
        b_btn_layout.addStretch()
        b_layout.addLayout(b_btn_layout)
        
        video_layout.addWidget(b_group)
        
        parent.addWidget(video_widget)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 创建控制面板标签页
        control_tabs = QTabWidget()

        # 融合参数控制面板
        self.control_panel = ControlPanel()
        control_tabs.addTab(self.control_panel, "融合参数")

        # 预览和日志标签页
        preview_log_widget = QWidget()
        preview_log_layout = QVBoxLayout(preview_log_widget)

        # 预览组
        preview_group = QGroupBox("融合预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_label = QLabel("点击'生成预览'查看融合效果")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(200)
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f5f5f5;")
        preview_layout.addWidget(self.preview_label)

        # 预览控制按钮
        preview_btn_layout = QHBoxLayout()
        self.preview_btn = QPushButton("生成预览")
        self.preview_btn.clicked.connect(self.generate_preview)
        preview_btn_layout.addWidget(self.preview_btn)

        self.clear_preview_btn = QPushButton("清除预览")
        self.clear_preview_btn.clicked.connect(self.clear_preview)
        preview_btn_layout.addWidget(self.clear_preview_btn)

        preview_btn_layout.addStretch()
        preview_layout.addLayout(preview_btn_layout)

        preview_log_layout.addWidget(preview_group)

        # 日志组
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("font-family: monospace; font-size: 9pt;")
        log_layout.addWidget(self.log_text)

        # 日志控制按钮
        log_btn_layout = QHBoxLayout()
        clear_log_btn = QPushButton("清除日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_btn_layout.addWidget(clear_log_btn)

        export_log_btn = QPushButton("导出日志")
        export_log_btn.clicked.connect(self.export_log)
        log_btn_layout.addWidget(export_log_btn)

        log_btn_layout.addStretch()
        log_layout.addLayout(log_btn_layout)

        preview_log_layout.addWidget(log_group)

        control_tabs.addTab(preview_log_widget, "预览和日志")

        # 性能监控标签页
        performance_widget = QWidget()
        performance_layout = QVBoxLayout(performance_widget)

        self.performance_text = QTextEdit()
        self.performance_text.setReadOnly(True)
        self.performance_text.setStyleSheet("font-family: monospace; font-size: 9pt;")
        performance_layout.addWidget(self.performance_text)

        # 性能控制按钮
        perf_btn_layout = QHBoxLayout()
        refresh_perf_btn = QPushButton("刷新性能")
        refresh_perf_btn.clicked.connect(self.refresh_performance)
        perf_btn_layout.addWidget(refresh_perf_btn)

        export_perf_btn = QPushButton("导出报告")
        export_perf_btn.clicked.connect(self.export_performance_report)
        perf_btn_layout.addWidget(export_perf_btn)

        perf_btn_layout.addStretch()
        performance_layout.addLayout(perf_btn_layout)

        control_tabs.addTab(performance_widget, "性能监控")

        parent.addWidget(control_tabs)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def connect_signals(self):
        """连接信号"""
        # 视频加载信号
        self.video_a_loaded.connect(self.on_video_a_loaded)
        self.video_b_loaded.connect(self.on_video_b_loaded)

        # 控制面板信号
        if hasattr(self, 'control_panel'):
            self.control_panel.parameters_changed.connect(self.on_parameters_changed)
            self.control_panel.preview_requested.connect(self.generate_preview)
            self.control_panel.fusion_requested.connect(self.start_fusion)

        # 定时器用于更新性能监控
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.update_performance_display)
        self.performance_timer.start(2000)  # 每2秒更新一次
    
    def load_video_a(self):
        """加载A视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择A视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )

        if file_path:
            self.load_video_a_file(file_path)

    def load_video_b(self):
        """加载B视频"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择B视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )

        if file_path:
            self.load_video_b_file(file_path)

    def load_video_a_file(self, file_path: str):
        """加载A视频文件"""
        try:
            self.log_message(f"正在加载A视频: {file_path}")

            if self.video_a_loader.load_video(file_path):
                self.current_video_a_path = file_path
                self.fusion_engine.load_video_a(file_path)
                self.video_a_loaded.emit(file_path)
                self.log_message(f"A视频加载成功: {file_path}")
            else:
                self.log_message(f"A视频加载失败: {file_path}", "ERROR")
                QMessageBox.warning(self, "错误", f"无法加载A视频文件:\n{file_path}")

        except Exception as e:
            self.log_message(f"A视频加载异常: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"加载A视频时发生错误:\n{str(e)}")

    def load_video_b_file(self, file_path: str):
        """加载B视频文件"""
        try:
            self.log_message(f"正在加载B视频: {file_path}")

            if self.video_b_loader.load_video(file_path):
                self.current_video_b_path = file_path
                self.fusion_engine.load_video_b(file_path)
                self.video_b_loaded.emit(file_path)
                self.log_message(f"B视频加载成功: {file_path}")
            else:
                self.log_message(f"B视频加载失败: {file_path}", "ERROR")
                QMessageBox.warning(self, "错误", f"无法加载B视频文件:\n{file_path}")

        except Exception as e:
            self.log_message(f"B视频加载异常: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"加载B视频时发生错误:\n{str(e)}")
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.accept()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        files = [u.toLocalFile() for u in event.mimeData().urls()]

        if files:
            # 检查文件扩展名
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
            video_files = [f for f in files if any(f.lower().endswith(ext) for ext in video_extensions)]

            if video_files:
                if len(video_files) == 1:
                    # 只有一个视频文件，询问用户要加载到A还是B
                    reply = QMessageBox.question(
                        self, "选择加载位置",
                        f"将视频文件加载到:\n{video_files[0]}\n\n选择'Yes'加载到A视频，'No'加载到B视频",
                        QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
                    )

                    if reply == QMessageBox.Yes:
                        self.load_video_a_file(video_files[0])
                    elif reply == QMessageBox.No:
                        self.load_video_b_file(video_files[0])

                elif len(video_files) >= 2:
                    # 多个视频文件，自动分配
                    self.load_video_a_file(video_files[0])
                    self.load_video_b_file(video_files[1])

                    if len(video_files) > 2:
                        self.log_message(f"检测到{len(video_files)}个视频文件，只加载前两个", "WARNING")
            else:
                QMessageBox.warning(self, "错误", "拖拽的文件中没有支持的视频格式")

    def on_video_a_loaded(self, file_path: str):
        """A视频加载完成"""
        import os
        filename = os.path.basename(file_path)
        self.video_a_label.setText(f"A视频已加载:\n{filename}")
        self.status_label.setText(f"A视频已加载: {filename}")

        # 显示视频信息
        if self.video_a_loader.is_loaded():
            info = self.video_a_loader.get_current_info()
            self.video_a_label.setToolTip(f"文件: {file_path}\n"
                                         f"分辨率: {info.width}x{info.height}\n"
                                         f"帧率: {info.fps:.2f} FPS\n"
                                         f"时长: {info.duration:.2f}秒")

        self.update_ui_state()

    def on_video_b_loaded(self, file_path: str):
        """B视频加载完成"""
        import os
        filename = os.path.basename(file_path)
        self.video_b_label.setText(f"B视频已加载:\n{filename}")
        self.status_label.setText(f"B视频已加载: {filename}")

        # 显示视频信息
        if self.video_b_loader.is_loaded():
            info = self.video_b_loader.get_current_info()
            self.video_b_label.setToolTip(f"文件: {file_path}\n"
                                         f"分辨率: {info.width}x{info.height}\n"
                                         f"帧率: {info.fps:.2f} FPS\n"
                                         f"时长: {info.duration:.2f}秒")

        self.update_ui_state()
    
    def update_ui_state(self):
        """更新UI状态"""
        # 检查是否可以开始融合
        can_start_fusion = (self.current_video_a_path and
                           self.current_video_b_path and
                           not self.is_fusion_running)

        # 更新按钮状态
        if hasattr(self, 'start_fusion_btn'):
            self.start_fusion_btn.setEnabled(can_start_fusion)
        if hasattr(self, 'stop_fusion_btn'):
            self.stop_fusion_btn.setEnabled(self.is_fusion_running)
        if hasattr(self, 'preview_btn'):
            self.preview_btn.setEnabled(can_start_fusion)

    def on_parameters_changed(self, params: dict):
        """参数变化处理"""
        try:
            # 更新融合引擎参数
            fusion_params = FusionParams()

            # 设置融合类型
            if params.get('fusion_type') == 'insertion':
                fusion_params.fusion_type = FusionType.INSERTION
            elif params.get('fusion_type') == 'overlay':
                fusion_params.fusion_type = FusionType.OVERLAY
            elif params.get('fusion_type') == 'blend':
                fusion_params.fusion_type = FusionType.BLEND

            # 设置其他参数
            fusion_params.alpha = params.get('alpha', 0.5)
            fusion_params.resize_mode = params.get('resize_mode', 'fit')
            fusion_params.output_fps = params.get('output_fps', 30.0)

            self.fusion_engine.set_fusion_params(fusion_params)
            self.log_message("融合参数已更新")

        except Exception as e:
            self.log_message(f"参数更新失败: {e}", "ERROR")

    def start_fusion(self):
        """开始融合"""
        if not self.current_video_a_path or not self.current_video_b_path:
            QMessageBox.warning(self, "警告", "请先加载A视频和B视频")
            return

        if self.is_fusion_running:
            QMessageBox.warning(self, "警告", "融合正在进行中")
            return

        try:
            self.is_fusion_running = True
            self.update_ui_state()

            self.status_label.setText("正在执行融合...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            self.log_message("开始融合处理...")

            # 在后台线程中执行融合
            self.fusion_thread = FusionThread(self.fusion_engine)
            self.fusion_thread.finished.connect(self.on_fusion_finished)
            self.fusion_thread.error.connect(self.on_fusion_error)
            self.fusion_thread.start()

        except Exception as e:
            self.is_fusion_running = False
            self.update_ui_state()
            self.log_message(f"启动融合失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"启动融合时发生错误:\n{str(e)}")

    def stop_fusion(self):
        """停止融合"""
        if not self.is_fusion_running:
            return

        try:
            if hasattr(self, 'fusion_thread') and self.fusion_thread.isRunning():
                self.fusion_thread.terminate()
                self.fusion_thread.wait(3000)  # 等待3秒

            self.is_fusion_running = False
            self.update_ui_state()

            self.status_label.setText("融合已停止")
            self.progress_bar.setVisible(False)
            self.log_message("融合处理已停止")

        except Exception as e:
            self.log_message(f"停止融合失败: {e}", "ERROR")
    
    def on_fusion_finished(self, success: bool):
        """融合完成处理"""
        self.is_fusion_running = False
        self.update_ui_state()
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText("融合处理完成")
            self.log_message("融合处理成功完成")
            QMessageBox.information(self, "成功", "视频融合处理完成！")
        else:
            self.status_label.setText("融合处理失败")
            self.log_message("融合处理失败", "ERROR")
            QMessageBox.warning(self, "失败", "视频融合处理失败，请检查日志")

    def on_fusion_error(self, error_msg: str):
        """融合错误处理"""
        self.is_fusion_running = False
        self.update_ui_state()
        self.progress_bar.setVisible(False)

        self.status_label.setText("融合处理出错")
        self.log_message(f"融合处理错误: {error_msg}", "ERROR")
        QMessageBox.critical(self, "错误", f"融合处理时发生错误:\n{error_msg}")

    def generate_preview(self):
        """生成预览"""
        if not self.current_video_a_path or not self.current_video_b_path:
            QMessageBox.warning(self, "警告", "请先加载A视频和B视频")
            return

        try:
            self.log_message("正在生成预览...")
            self.status_label.setText("正在生成预览...")

            # 获取预览帧
            preview_frames = self.fusion_engine.get_fusion_preview(max_frames=3)

            if preview_frames:
                # 显示第一个预览帧
                frame_index, frame = preview_frames[0]

                # 转换为QPixmap并显示
                import cv2
                from PyQt5.QtGui import QImage

                height, width, channel = frame.shape
                bytes_per_line = 3 * width
                q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

                # 缩放图像以适应标签
                pixmap = QPixmap.fromImage(q_image)
                scaled_pixmap = pixmap.scaled(self.preview_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)

                self.preview_label.setPixmap(scaled_pixmap)
                self.preview_label.setText("")

                self.log_message(f"预览生成完成，显示第{frame_index}帧")
                self.status_label.setText("预览生成完成")
            else:
                self.log_message("预览生成失败：无预览帧", "WARNING")
                QMessageBox.warning(self, "警告", "无法生成预览，请检查融合参数")

        except Exception as e:
            self.log_message(f"预览生成失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"生成预览时发生错误:\n{str(e)}")

    def clear_preview(self):
        """清除预览"""
        self.preview_label.clear()
        self.preview_label.setText("点击'生成预览'查看融合效果")
        self.log_message("预览已清除")

    def export_video(self):
        """导出视频"""
        if not hasattr(self.fusion_engine, 'result_frames') or not self.fusion_engine.result_frames:
            QMessageBox.warning(self, "警告", "没有可导出的融合结果，请先执行融合")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存融合视频", "",
            "MP4文件 (*.mp4);;AVI文件 (*.avi);;所有文件 (*)"
        )

        if file_path:
            try:
                self.log_message(f"正在导出视频到: {file_path}")
                self.status_label.setText("正在导出视频...")
                self.progress_bar.setVisible(True)
                self.progress_bar.setRange(0, 0)

                # 执行导出
                success = self.fusion_engine.save_result(file_path)

                self.progress_bar.setVisible(False)

                if success:
                    self.log_message(f"视频导出成功: {file_path}")
                    self.status_label.setText("视频导出完成")
                    QMessageBox.information(self, "成功", f"视频已成功导出到:\n{file_path}")
                else:
                    self.log_message(f"视频导出失败: {file_path}", "ERROR")
                    QMessageBox.warning(self, "失败", "视频导出失败，请检查文件路径和权限")

            except Exception as e:
                self.progress_bar.setVisible(False)
                self.log_message(f"视频导出异常: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"导出视频时发生错误:\n{str(e)}")
    
    def log_message(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        if level == "ERROR":
            formatted_msg = f"[{timestamp}] ❌ {message}"
            self.logger.error(message)
        elif level == "WARNING":
            formatted_msg = f"[{timestamp}] ⚠️ {message}"
            self.logger.warning(message)
        else:
            formatted_msg = f"[{timestamp}] ℹ️ {message}"
            self.logger.info(message)

        if hasattr(self, 'log_text'):
            self.log_text.append(formatted_msg)
            # 自动滚动到底部
            self.log_text.moveCursor(self.log_text.textCursor().End)

    def clear_log(self):
        """清除日志"""
        if hasattr(self, 'log_text'):
            self.log_text.clear()
            self.log_message("日志已清除")

    def export_log(self):
        """导出日志"""
        if not hasattr(self, 'log_text'):
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志文件", "",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())

                self.log_message(f"日志已导出到: {file_path}")
                QMessageBox.information(self, "成功", f"日志已导出到:\n{file_path}")

            except Exception as e:
                self.log_message(f"日志导出失败: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"导出日志时发生错误:\n{str(e)}")

    def update_performance_display(self):
        """更新性能显示"""
        if not hasattr(self, 'performance_text'):
            return

        try:
            # 获取性能统计
            stats = self.fusion_engine.get_performance_stats()

            # 格式化显示
            display_text = "=== 性能监控 ===\n\n"

            # 性能监控信息
            if "performance_monitor" in stats:
                perf_data = stats["performance_monitor"]
                display_text += "📊 系统性能:\n"

                if "current" in perf_data and perf_data["current"]:
                    current = perf_data["current"]
                    display_text += f"  CPU使用率: {current.get('cpu_percent', 0):.1f}%\n"
                    display_text += f"  内存使用率: {current.get('memory_percent', 0):.1f}%\n"
                    display_text += f"  内存使用量: {current.get('memory_used_mb', 0):.0f}MB\n"

                if "average" in perf_data:
                    avg = perf_data["average"]
                    display_text += f"\n📈 平均性能:\n"
                    display_text += f"  平均CPU: {avg.get('avg_cpu_percent', 0):.1f}%\n"
                    display_text += f"  平均内存: {avg.get('avg_memory_percent', 0):.1f}%\n"

            # 内存管理信息
            if "memory_manager" in stats:
                mem_data = stats["memory_manager"]
                display_text += f"\n💾 内存管理:\n"

                if "memory_info" in mem_data:
                    mem_info = mem_data["memory_info"]
                    display_text += f"  系统总内存: {mem_info.get('total_mb', 0):.0f}MB\n"
                    display_text += f"  可用内存: {mem_info.get('available_mb', 0):.0f}MB\n"
                    display_text += f"  进程内存: {mem_info.get('process_mb', 0):.0f}MB\n"

                if "cache_stats" in mem_data:
                    cache_stats = mem_data["cache_stats"]
                    display_text += f"  缓存项目: {cache_stats.get('items', 0)}\n"
                    display_text += f"  缓存大小: {cache_stats.get('total_size_mb', 0):.1f}MB\n"

            # 线程池信息
            if "thread_pool" in stats and stats["thread_pool"]:
                thread_data = stats["thread_pool"]
                display_text += f"\n🔄 线程池:\n"
                display_text += f"  最大线程数: {thread_data.get('max_workers', 0)}\n"
                display_text += f"  活动任务: {thread_data.get('active_tasks', 0)}\n"
                display_text += f"  成功率: {thread_data.get('success_rate', 0):.1f}%\n"

            self.performance_text.setText(display_text)

        except Exception as e:
            self.performance_text.setText(f"性能监控错误: {str(e)}")

    def refresh_performance(self):
        """刷新性能显示"""
        self.update_performance_display()
        self.log_message("性能监控已刷新")

    def export_performance_report(self):
        """导出性能报告"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存性能报告", "",
            "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                success = self.fusion_engine.export_performance_report(file_path)

                if success:
                    self.log_message(f"性能报告已导出到: {file_path}")
                    QMessageBox.information(self, "成功", f"性能报告已导出到:\n{file_path}")
                else:
                    self.log_message(f"性能报告导出失败: {file_path}", "ERROR")
                    QMessageBox.warning(self, "失败", "性能报告导出失败")

            except Exception as e:
                self.log_message(f"性能报告导出异常: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"导出性能报告时发生错误:\n{str(e)}")

    def toggle_performance_monitor(self, checked: bool):
        """切换性能监控显示"""
        # 这里可以添加性能监控窗口的显示/隐藏逻辑
        self.log_message(f"性能监控显示: {'开启' if checked else '关闭'}")

    # 项目管理方法
    def new_project(self):
        """新建项目"""
        reply = QMessageBox.question(
            self, "新建项目",
            "创建新项目将清除当前所有设置，是否继续？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.reset_project()
            self.log_message("新项目已创建")

    def open_project(self):
        """打开项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目文件", "",
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                # TODO: 实现项目加载逻辑
                self.log_message(f"项目加载功能开发中: {file_path}", "WARNING")
                QMessageBox.information(self, "提示", "项目加载功能正在开发中")

            except Exception as e:
                self.log_message(f"项目加载失败: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"加载项目时发生错误:\n{str(e)}")

    def save_project(self):
        """保存项目"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目文件", "",
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                # TODO: 实现项目保存逻辑
                self.log_message(f"项目保存功能开发中: {file_path}", "WARNING")
                QMessageBox.information(self, "提示", "项目保存功能正在开发中")

            except Exception as e:
                self.log_message(f"项目保存失败: {e}", "ERROR")
                QMessageBox.critical(self, "错误", f"保存项目时发生错误:\n{str(e)}")

    def batch_process(self):
        """批量处理"""
        self.log_message("批量处理功能开发中", "WARNING")
        QMessageBox.information(self, "提示", "批量处理功能正在开发中")

    def open_preset_manager(self):
        """打开预设管理器"""
        try:
            preset_manager = PresetManager(self)
            preset_manager.exec_()

        except Exception as e:
            self.log_message(f"打开预设管理器失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"打开预设管理器时发生错误:\n{str(e)}")

    def show_manual(self):
        """显示用户手册"""
        QMessageBox.information(self, "用户手册",
                               "用户手册功能正在开发中\n\n"
                               "基本使用步骤：\n"
                               "1. 加载A视频和B视频\n"
                               "2. 设置融合参数\n"
                               "3. 生成预览查看效果\n"
                               "4. 开始融合处理\n"
                               "5. 导出融合结果")

    def reset_project(self):
        """重置项目"""
        # 重置视频
        self.current_video_a_path = None
        self.current_video_b_path = None

        # 重置UI
        self.video_a_label.setText("点击加载A视频")
        self.video_b_label.setText("点击加载B视频")
        self.clear_preview()

        # 重置融合引擎
        self.fusion_engine.reset()

        # 更新UI状态
        self.update_ui_state()

        self.status_label.setText("项目已重置")

    def reset_layout(self):
        """重置布局"""
        self.status_label.setText("布局已重置")
        self.log_message("布局已重置")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                         f"{self.config.get('application.name', '视频融合编辑器')}\n"
                         f"版本: {self.config.get('application.version', '1.0.0')}\n\n"
                         "桌面端视频编辑应用，实现A视频与B视频的多维度融合功能。\n\n"
                         "功能特点：\n"
                         "• 多种融合模式（插入、叠加、混合）\n"
                         "• 实时预览和参数调整\n"
                         "• 性能监控和优化\n"
                         "• 参数预设管理\n"
                         "• 拖拽文件支持")

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_fusion_running:
            reply = QMessageBox.question(
                self, "确认退出",
                "融合处理正在进行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

            # 停止融合处理
            self.stop_fusion()

        self.logger.info("应用程序退出")
        event.accept()
