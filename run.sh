#!/bin/bash

# 视频融合编辑器启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "=== 启动视频融合编辑器 ==="

# 激活环境
source "${SCRIPT_DIR}/activate_env.sh"

if [ $? -ne 0 ]; then
    echo "错误: 环境激活失败"
    exit 1
fi

# 检查主程序文件
MAIN_FILE="${SCRIPT_DIR}/src/main.py"
if [ ! -f "${MAIN_FILE}" ]; then
    echo "错误: 未找到主程序文件 ${MAIN_FILE}"
    echo "请先完成项目开发"
    exit 1
fi

# 启动应用
echo "启动应用..."
cd "${SCRIPT_DIR}"
python src/main.py

echo "=== 应用已退出 ==="
