#!/usr/bin/env python3
"""
GUI测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication
    from src.gui.main_window import MainWindow
    
    print("正在创建QApplication...")
    app = QApplication(sys.argv)
    
    print("正在创建主窗口...")
    window = MainWindow()
    
    print("显示主窗口...")
    window.show()
    
    print("GUI测试成功！窗口已显示。")
    print("按Ctrl+C退出测试")
    
    # 运行3秒后自动退出
    from PyQt5.QtCore import QTimer
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(3000)  # 3秒
    
    app.exec_()
    print("GUI测试完成")
    
except Exception as e:
    print(f"GUI测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
