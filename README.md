# 视频融合编辑器 (Video Fusion Editor)

一个桌面端视频编辑应用，实现A视频与B视频的多维度融合功能。

## 功能特性

### 核心功能
- **视频插入**: A视频直接插入或预处理后插入B视频指定位置
- **视频融合**: 多种融合方式，支持固定位置或连续移动
- **文字叠加**: 在视频帧上添加可控制的文字
- **预处理**: 边缘提取、直方图匹配、缩放等

### 融合方式
- 直接插入
- 预处理插入（边缘提取、直方图匹配、缩放）
- 分段插入
- 叠加覆盖
- 线性加权融合

### 隐蔽性设计
- 保持B视频内容可见性
- 降低A视频检测难度
- 灵活的参数控制

## 环境要求

- Python 3.9+
- Conda (推荐)
- macOS/Windows/Linux

## 安装和运行

### 1. 环境配置
```bash
# 激活环境并安装依赖
./activate_env.sh
```

### 2. 运行应用
```bash
# 启动应用
./run.sh
```

### 3. 运行测试
```bash
# 激活环境
source activate_env.sh

# 运行测试
python -m pytest tests/
```

## 项目结构

```
video-fusion-editor/
├── src/                    # 源代码
│   ├── gui/               # GUI界面模块
│   ├── video/             # 视频处理模块
│   ├── fusion/            # 融合算法模块
│   ├── effects/           # 特效处理模块
│   ├── utils/             # 工具函数
│   └── main.py            # 主程序入口
├── tests/                 # 测试文件
├── assets/                # 资源文件
├── docs/                  # 文档
├── config.json            # 配置文件
├── requirements.txt       # Python依赖
├── activate_env.sh        # 环境激活脚本
├── run.sh                # 启动脚本
└── README.md             # 项目说明
```

## 开发状态

- [x] 项目环境配置
- [x] 基础项目结构搭建
- [ ] GUI主界面开发
- [ ] 视频加载模块
- [ ] 视频插入功能
- [ ] 视频预处理模块
- [ ] 文字叠加功能
- [ ] 视频融合算法
- [ ] 参数控制界面
- [ ] 测试和优化

## 技术栈

- **GUI框架**: PyQt5
- **视频处理**: OpenCV, FFmpeg
- **图像处理**: PIL, NumPy, SciPy
- **科学计算**: scikit-image, matplotlib
- **测试框架**: pytest, pytest-qt

## 许可证

本项目采用MIT许可证。
